/* eslint-disable max-lines */
import {useCallback, useEffect, useRef, useState, KeyboardEvent} from 'react';
import {Flex, Input} from 'antd';
import {UI_DIMENSIONS} from '../constants';
import SendButton from './SendButton';

import {
    ChatMessageInputProps,
    getInputRef,
    shouldBeMultiLine,
    createInputProps,
} from './utils';

const ChatMessageInput = ({
    disabled = false,
    disabledReason = '',
    onSend,
    onStop,
    isGenerating = false,
    placeholder = '输入消息，回车发送，Shift+回车换行...',
}: ChatMessageInputProps) => {
    const [content, setContent] = useState('');
    const [sending, setSending] = useState(false);
    const [isMultiLine, setIsMultiLine] = useState(false);
    const inputRef = useRef<HTMLTextAreaElement>(null);

    useEffect(
        () => {
            setIsMultiLine(shouldBeMultiLine(content));
        },
        [content]
    );

    const handleSend = useCallback(
        async () => {
            if (!content.trim() || disabled || sending) {
                return;
            }

            setSending(true);
            try {
                await onSend(content.trim());
                setContent('');
                setIsMultiLine(false);
            } catch (error) {
                console.error('发送消息失败:', error);
            } finally {
                setSending(false);
            }
        },
        [content, disabled, sending, onSend]
    );
    const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.metaKey && !e.ctrlKey && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    const canSend = content.trim() && !disabled && !sending && !isGenerating;

    const renderButton = () => {
        return (
            <SendButton
                canSend={canSend}
                isGenerating={isGenerating}
                sending={sending}
                onSend={handleSend}
                onStop={onStop}
            />
        );
    };

    return (
        <Flex
            vertical
            justify="flex-end"
            style={{
                width: '100%',
                borderRadius: '4px',
                border: '1px solid #D9D9D9',
                backgroundColor: '#FAFAFA',
                minHeight: '42px',
            }}
        >
            {isMultiLine ? (
                <>
                    <Flex vertical align="flex-start" style={{padding: '0 12px', flex: 1, minHeight: 'auto'}}>
                        <Input.TextArea
                            ref={ref => {
                                inputRef.current = getInputRef(ref);
                            }}
                            onChange={e => setContent(e.target.value)}
                            onKeyDown={handleKeyDown}
                            style={{
                                resize: 'none',
                                width: '100%',
                                fontSize: `${UI_DIMENSIONS.FONT_SIZE_MEDIUM}px`,
                                fontWeight: 400,
                                lineHeight: '22px',
                                border: 'none',
                                boxShadow: 'none',
                                padding: '10px 0',
                                backgroundColor: 'transparent',
                            }}
                            {...createInputProps(
                                content,
                                disabled,
                                disabledReason,
                                placeholder,
                                true
                            )}
                        />
                    </Flex>
                    <Flex justify="flex-end" style={{padding: '7px 12px'}}>
                        {renderButton()}
                    </Flex>
                </>
            ) : (
                <Flex align="center" justify="space-between" style={{height: '100%', padding: '0 12px'}}>
                    <Input.TextArea
                        ref={ref => {
                            inputRef.current = getInputRef(ref);
                        }}
                        onChange={e => setContent(e.target.value)}
                        onKeyDown={handleKeyDown}
                        style={{
                            resize: 'none',
                            width: '100%',
                            fontSize: `${UI_DIMENSIONS.FONT_SIZE_MEDIUM}px`,
                            fontWeight: 400,
                            lineHeight: '22px',
                            border: 'none',
                            boxShadow: 'none',
                            padding: '10px 0',
                            backgroundColor: 'transparent',
                        }}
                        {...createInputProps(
                            content,
                            disabled,
                            disabledReason,
                            placeholder,
                            false
                        )}
                    />
                    <Flex align="center" justify="center">
                        {renderButton()}
                    </Flex>
                </Flex>
            )}
        </Flex>
    );
};

export default ChatMessageInput;
